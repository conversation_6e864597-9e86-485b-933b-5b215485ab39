import Image from "next/image";
import Link from "next/link";
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: '<PERSON><PERSON><PERSON><PERSON>ję za kontakt | Angielski z Panią Kasią',
  description: 'D<PERSON><PERSON>kuję za wypełnienie formularza kontaktowego. Skontaktuję się z Tobą tak szybko jak to możliwe!',
  robots: {
    index: false,
    follow: true,
  },
};

export default function ContactSuccessPage() {
  return (
    <div className="flex flex-col justify-between bg-white relative overflow-hidden xl:pt-12">
      <div className="flex flex-col max-w-7xl mx-auto w-full pt-16 pb-28 px-4 md:px-8 lg:px-20 gap-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-8">
          <div className="flex-1 flex flex-col justify-center z-10">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-['Montserrat',Helvetica] text-[#114438] mb-6 leading-tight">
              <PERSON><PERSON><PERSON><PERSON>ję za wypełnienie<br />formularza kontaktowego
            </h1>
            <p className="text-xl md:text-2xl text-[#114438] mb-8 font-['Montserrat',Helvetica]">
              Skontaktuję się z Tobą tak szybko jak to możliwe!
            </p>
            <div className="mb-8">
              <Image src="/podpis.png" alt="Podpis" width={260} height={80} />
            </div>
            <Link href="/" className="inline-block">
              <button className="rounded-full px-8 py-4 bg-gradient-to-r from-[#1ca9e2] to-[#73b737] text-white font-bold font-['Montserrat',Helvetica] text-lg shadow-lg transition hover:opacity-90">
                WRÓĆ NA STRONĘ GŁÓWNĄ
              </button>
            </Link>
          </div>
        </div>
      </div>
      <Image
        src="/contact-success.png"
        alt="Success Illustration"
        width={500}
        height={560}
        className="absolute right-0 bottom-0 w-[260px] md:w-[340px] lg:w-[400px] max-md:hidden h-auto z-0 select-none pointer-events-none opacity-60"
        priority
      />
    </div>
  );
} 