"use client";
import React from "react";
import Link from "next/link";

export const FooterSection = (): React.JSX.Element => {
  const navigationLinks = [
    { label: "Ofert<PERSON> z<PERSON>", sectionId: "oferta-zajec" },
    { label: "<PERSON>nn<PERSON>", sectionId: "cennik" },
    { label: "Opinie", sectionId: "opinie" },
    { label: "Kontakt", href: "/contact" },
  ];

  // Social icons as in HeroSection, with white fill
  const socialIcons = [
    {
      name: 'Instagram',
      href: 'https://www.instagram.com/', // Replace with actual link
      svg: (
        <svg width="32" height="31" viewBox="0 0 32 31" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clipPath="url(#clip0_4_324)">
            <path d="M16.0015 18.7276C17.8423 18.7276 19.3346 17.2819 19.3346 15.4986C19.3346 13.7153 17.8423 12.2696 16.0015 12.2696C14.1606 12.2696 12.6683 13.7153 12.6683 15.4986C12.6683 17.2819 14.1606 18.7276 16.0015 18.7276Z" fill="white"/>
            <path d="M20.5845 7.42612H11.4184C9.34972 7.42612 7.66858 9.05755 7.66858 11.0587V19.9413C7.66858 21.9425 9.34972 23.5739 11.4184 23.5739H20.5845C22.6532 23.5739 24.3343 21.9425 24.3343 19.9413V11.0587C24.3343 9.05755 22.6532 7.42612 20.5845 7.42612ZM16.0015 20.3449C13.2423 20.3449 11.0017 18.1715 11.0017 15.4986C11.0017 12.8256 13.2452 10.6551 16.0015 10.6551C18.7577 10.6551 21.0012 12.8285 21.0012 15.4986C21.0012 18.1687 18.7577 20.3449 16.0015 20.3449ZM21.3129 11.4624C20.6807 11.4624 20.1679 10.9656 20.1679 10.3531C20.1679 9.7406 20.6807 9.24384 21.3129 9.24384C21.9452 9.24384 22.458 9.7406 22.458 10.3531C22.458 10.9656 21.9452 11.4624 21.3129 11.4624Z" fill="white"/>
            <path d="M16.0015 0C7.16453 0 0 6.93781 0 15.4986C0 24.0594 7.16453 31 16.0015 31C24.8384 31 32 24.0594 32 15.4986C32 6.93781 24.8384 0 16.0015 0ZM26.0009 19.9413C26.0009 22.8344 23.571 25.1884 20.5845 25.1884H11.4184C8.42903 25.1884 5.99909 22.8344 5.99909 19.9384V11.0587C5.99909 8.16562 8.42903 5.81162 11.4184 5.81162H20.5845C23.571 5.81162 26.0009 8.16562 26.0009 11.0587V19.9413Z" fill="white"/>
          </g>
          <defs>
            <clipPath id="clip0_4_324">
              <rect width="32" height="31" fill="white"/>
            </clipPath>
          </defs>
        </svg>
      ),
    },
    {
      name: 'Facebook',
      href: 'https://www.facebook.com/', // Replace with actual link
      svg: (
        <svg width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clipPath="url(#clip0_1_16)">
            <path d="M31 15.5396C31 23.5769 24.9151 30.1906 17.1115 31V19.6714H21.1446L21.8078 15.6019H17.1115V12.5002C17.1115 11.2182 17.453 10.3069 19.2875 10.3069H21.9997V6.20623C21.9997 6.20623 20.2781 5.827 18.6638 5.827C15.294 5.827 13.0615 8.05989 13.0615 11.8464V15.6019H9.00027V19.6714H13.0615V30.8896C5.65869 29.718 0 23.291 0 15.5396C0 6.95618 6.94 0 15.5 0C24.06 0 31 6.95618 31 15.5396Z" fill="white"/>
          </g>
          <defs>
            <clipPath id="clip0_1_16">
              <rect width="31" height="31" fill="white"/>
            </clipPath>
          </defs>
        </svg>
      ),
    },
    {
      name: 'LinkedIn',
      href: '#', // Replace with actual link
      svg: (
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect width="32" height="32" rx="6" fill="white" fillOpacity="0.1"/>
          <path d="M24 24H21.3333V19.3333C21.3333 18.3933 21.32 17.1733 20.0267 17.1733C18.72 17.1733 18.5333 18.2133 18.5333 19.2533V24H15.8667V14.6667H18.4133V15.8667H18.4533C18.8133 15.1733 19.68 14.44 20.8933 14.44C23.5733 14.44 24 16.16 24 18.0533V24ZM12.6667 13.4667C11.8667 13.4667 11.2 12.8 11.2 12C11.2 11.2 11.8667 10.5333 12.6667 10.5333C13.4667 10.5333 14.1333 11.2 14.1333 12C14.1333 12.8 13.4667 13.4667 12.6667 13.4667ZM14 24H11.3333V14.6667H14V24ZM25.3333 8H6.66667C6.3 8 6 8.3 6 8.66667V25.3333C6 25.7 6.3 26 6.66667 26H25.3333C25.7 26 26 25.7 26 25.3333V8.66667C26 8.3 25.7 8 25.3333 8Z" fill="white"/>
        </svg>
      ),
    },
  ];

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="w-full bg-gradient-to-r from-[#73B737] to-[#1CA9E2] py-16">
      <div className="max-w-7xl mx-auto px-4 md:px-8 lg:px-20">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Logo and Description */}
          <div className="lg:col-span-2">
            <p className="font-['Montserrat',Helvetica] font-normal text-gray-100 text-base leading-relaxed mb-6">
              Profesjonalna nauka języka angielskiego z doświadczoną lektorką. 
              Oferuję indywidualne podejście, przygotowanie do egzaminów i zajęcia 
              dostosowane do Twoich potrzeb.
            </p>
            <div className="flex items-center gap-4">
              <span className="font-['Montserrat',Helvetica] font-medium text-white text-base">
                Śledź nas:
              </span>
              {socialIcons.map((icon) => (
                <a
                  key={icon.name}
                  href={icon.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:scale-110 transition-transform duration-200"
                  aria-label={icon.name}
                >
                  {icon.svg}
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-['Montserrat',Helvetica] font-bold text-white text-lg mb-6">
              Szybkie linki
            </h3>
            <ul className="space-y-3">
              {navigationLinks.map((link, index) => (
                <li key={index}>
                  {typeof link.href === 'string' ? (
                    <Link
                      href={link.href}
                      className="font-['Montserrat',Helvetica] font-normal text-gray-100 text-base hover:text-white transition-colors cursor-pointer text-left"
                    >
                      {link.label}
                    </Link>
                  ) : (
                    <Link
                      href={`/#${link.sectionId}`}
                      className="font-['Montserrat',Helvetica] font-normal text-gray-100 text-base hover:text-white transition-colors cursor-pointer text-left"
                      onClick={e => {
                        if (window.location.pathname === "/") {
                          e.preventDefault();
                          scrollToSection(link.sectionId);
                        }
                      }}
                    >
                      {link.label}
                    </Link>
                  )}
                </li>
              ))}
              <li>
                <Link
                  href="/polityka-prywatnosci"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-['Montserrat',Helvetica] font-normal text-gray-100 text-base hover:text-white transition-colors"
                >
                  Polityka Prywatności
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Information */}
          <div>
            <h3 className="font-['Montserrat',Helvetica] font-bold text-white text-lg mb-6">
              Kontakt
            </h3>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 mt-0.5">
                  <svg className="w-full h-full text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                </div>
                <div>
                  <p className="font-['Montserrat',Helvetica] font-medium text-white text-sm">Email</p>
                  <a 
                    href="mailto:<EMAIL>"
                    className="font-['Montserrat',Helvetica] font-normal text-gray-100 text-base hover:text-white transition-colors"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-5 h-5 mt-0.5">
                  <svg className="w-full h-full text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                  </svg>
                </div>
                <div>
                  <p className="font-['Montserrat',Helvetica] font-medium text-white text-sm">Telefon</p>
                  <a 
                    href="tel:+48123456789"
                    className="font-['Montserrat',Helvetica] font-normal text-gray-100 text-base hover:text-white transition-colors"
                  >
                    +48 123 456 789
                  </a>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-5 h-5 mt-0.5">
                  <svg className="w-full h-full text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p className="font-['Montserrat',Helvetica] font-medium text-white text-sm">Lokalizacja</p>
                  <p className="font-['Montserrat',Helvetica] font-normal text-gray-100 text-base">
                    Warszawa
                  </p>
                  <p className="font-['Montserrat',Helvetica] font-normal text-gray-200 text-sm">
                    Zajęcia online i stacjonarne
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Border and Copyright */}
        <div className="border-t border-white/30 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="font-['Montserrat',Helvetica] font-normal text-gray-200 text-sm text-center md:text-left">
              © 2025 Katarzyna Tyszkiewicz - Angielski z Panią Kasią. Wszystkie prawa zastrzeżone.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};
