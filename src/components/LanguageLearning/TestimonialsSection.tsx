import React from "react";
import Image from "next/image";
import { Card } from "@/components/ui/card";

export const TestimonialsSection = (): React.JSX.Element => {
  // Testimonial data for reusability
  const testimonials = [
    {
      id: 1,
      content:
        "Bardzo polecam zajęcia z Panią Kasią, dzięki nim udało mi się zdać egzamin językowy na studiach, a mój angielski się poprawił. Kasia jest świetną nauczycielką, bardzo pomocną i wyrozumiałą. Zajęcia odbywały się w świetnej atmosferze.",
      author: "Andżelika",
    },
    {
      id: 2,
      content:
        "Chciałbym serdecznie polecić panią Kasie . Dzięki jego profesjonalnemu podejściu i zaangażowaniu, byłem świetnie przygotowany do matury. Lekcje były interesujące i dobrze zorganizowane, a materiały dydaktyczne bardzo pomocne. Nauczyciel zawsze był cierpliwy i gotowy do pomocy. Dzięki niemu zdobyłem nie tylko wiedzę, ale i pewność siebie. Gorąco polecam!",
      author: "Dawid",
    },
    {
      id: 3,
      content:
        "Dostałam dofinansowanie z firmy na rozwijanie języka angielskiego. Pierwszy raz czuję, że mam szansę nauczyć się tego języka z Panią Kasią. Uważam, że stosuje genialną metodę wracania do materiału i powtarzania bloków tematycznych po kilka razy ale w różnorodny i ciekawy sposób. Ja jestem zachwycona!",
      author: "Agnieszka",
    },
    {
      id: 4,
      content:
        "Pani Kasia to absolutnie najlepsza nauczycielka języka angielskiego, jaką miałem. Jej lekcje są zawsze starannie przygotowane i pełne interesujących materiałów. Potrafi świetnie wyjaśnić nawet najbardziej skomplikowane zagadnienia, a jej cierpliwość i zaangażowanie sprawiają, że nauka staje się przyjemnością. Dzięki niej szybko poprawiłem swoje umiejętności językowe. Gorąco polecam!",
      author: "Vladimir",
    },
    {
      id: 5,
      content:
        "Zajęcia z Kasią okazały się dla mnie wybawieniem. Bardzo długo tkwiłam w przekonaniu, że nie mam zdolności do języków obcych. Kasia udowodniła mi, że to nieprawda. Jej metody nauczania są skuteczne i przyjazne. Dzięki niej w końcu przełamałam barierę językową i mogę swobodnie komunikować się po angielsku.",
      author: "Monika",
    },
    {
      id: 6,
      content: "This is a testimonial with a gradient background and custom icon colors.",
      author: "Gradient Style",
    },
  ];

  // Define color/style configs to cycle through
  const testimonialStyles = [
    {
      bgColor: "bg-[#66caff33]",
      iconBgColor: "bg-[#144969]",
      iconColor: "#66CAFF",
      style: undefined,
    },
    {
      bgColor: "bg-[#66caff33]",
      iconBgColor: "bg-[#162343]",
      iconColor: "#66CAFF",
      style: undefined,
    },
    {
      bgColor: "bg-[#9ee75d33]",
      iconBgColor: "bg-[#9ee75d]",
      iconColor: "#2B510A",
      style: undefined,
    },
    {
      bgColor: "gradient",
      iconBgColor: "#1CA9E2",
      iconColor: "#162343",
      style: "gradient",
    },
  ];

  // Split testimonials for masonry columns
  const mid = Math.ceil(testimonials.length / 2);
  const col1 = testimonials.slice(0, mid);
  const col2 = testimonials.slice(mid).reverse();

  return (
    <section className="px-4 md:px-8 lg:px-20 py-16 bg-white">
      <h2 className="font-['Montserrat',Helvetica] font-bold text-black text-[32px] mb-20">
        Opinie
      </h2>

      <div className="relative max-w-6xl mx-auto flex flex-col lg:flex-row lg:items-start">
        {/* Decorative Illustration - Absolutely Positioned */}
        <Image
          src="/standing-illustration.png"
          alt="Decorative illustration"
          className="hidden lg:block absolute -right-12 top-[300px] translate-y-[-45%] w-[350px] h-auto z-50 max-w-[30vw]"
          width={350}
          height={400}
          style={{ opacity: 1 }}
        />

        {/* Masonry Columns */}
        <div className="w-full lg:w-4/5 flex flex-col lg:flex-row gap-8 relative z-20">
          {/* Single column on mobile, two columns on lg */}
          <div className="flex-1 flex flex-col gap-8">
            {(col1).map((testimonial, index) => {
              const style = testimonialStyles[index % testimonialStyles.length];
              return (
                <div key={testimonial.id} className="mb-8 relative">
                  <Card className="border-none rounded-[20px] bg-white relative">
                    {/* Absolutely positioned quote icon shape */}
                    <div
                      className={`absolute left-10 -top-[52px] z-30 w-[107px] h-[105px] rounded-[0px_15px_0px_15px] flex items-center justify-center${style?.iconBgColor && style?.iconBgColor.startsWith('bg-') ? ' ' + style?.iconBgColor : ''}`}
                      style={{
                        boxShadow: '0 4px 16px 0 rgba(0,0,0,0.08)',
                        background: style?.style === 'gradient'
                          ? '#1CA9E2'
                          : style?.iconBgColor && style?.iconBgColor.startsWith('bg-')
                            ? undefined
                            : style?.iconBgColor,
                      }}
                    >
                      <svg width="69" height="43" viewBox="0 0 69 43" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clipPath="url(#clip0_58_44)">
                          <path d="M30.3817 8.86725V19.3755C30.3817 20.1315 30.3817 20.9169 30.3699 21.7315V21.796C30.3699 32.5093 29.0905 43 14.7575 43C13.6549 43 12.6349 42.9473 11.6739 42.8242C15.5475 40.3627 18.1064 36.102 18.1064 31.2551C18.1064 30.9035 18.0946 30.5519 18.0651 30.2061H8.9264C4.0033 30.2061 0.00586589 26.2325 0.00586589 21.3388V8.86725C-3.00352e-05 3.97356 3.99741 0 8.9205 0H21.4611C26.3842 0 30.3817 3.97356 30.3817 8.86725ZM60.0794 0H47.5388C42.6157 0 38.6183 3.97356 38.6183 8.86725V21.333C38.6183 26.2267 42.6157 30.2002 47.5388 30.2002H56.6775C56.7129 30.5519 56.7188 30.9035 56.7188 31.2493C56.7188 36.0961 54.1658 40.3568 50.2863 42.8183C51.2414 42.9414 52.2673 42.9941 53.3699 42.9941C67.7088 42.9941 68.9823 32.5035 68.9823 21.7901V21.7256C68.9941 20.9169 68.9941 20.1257 68.9941 19.3696V8.86725C68.9941 3.97356 64.9966 0 60.0735 0H60.0794Z"
                            fill={style?.style === 'gradient' ? '#162343' : style?.iconBgColor === 'bg-[#9ee75d]' ? '#2B510A' : '#66CAFF'} />
                        </g>
                        <defs>
                          <clipPath id="clip0_58_44">
                            <rect width="69" height="43" fill="white"/>
                          </clipPath>
                        </defs>
                      </svg>
                    </div>
                    {/* Inner wrapper for background and overflow-hidden */}
                    <div
                      className={`p-6 pt-20 rounded-[20px] overflow-hidden${style?.style !== 'gradient' ? ' ' + style?.bgColor : ''}`}
                      style={
                        style?.style === 'gradient'
                          ? { background: 'linear-gradient(to bottom, #66CAFF 0%, rgba(102,202,255,0) 100%)' }
                          : undefined
                      }
                    >
                      <div className="font-['Poppins',Helvetica] font-normal text-black text-base">
                        <p className="mb-4">{testimonial.content}</p>
                        <p className="font-light italic">{testimonial.author}</p>
                      </div>
                    </div>
                  </Card>
                </div>
              );
            })}
          </div>
          <div className="hidden lg:flex-1 lg:flex lg:flex-col lg:gap-8">
            {(col2).map((testimonial, index) => {
              const reversedStyles = [...testimonialStyles].reverse();
              const style = reversedStyles[index % reversedStyles.length];
              return (
                <div key={testimonial.id} className="mb-8 relative">
                  <Card className="border-none rounded-[20px] bg-white relative">
                    {/* Absolutely positioned quote icon shape */}
                    <div
                      className={`absolute left-10 -top-[52px] z-30 w-[107px] h-[105px] rounded-[0px_15px_0px_15px] flex items-center justify-center${style?.iconBgColor && style?.iconBgColor.startsWith('bg-') ? ' ' + style?.iconBgColor : ''}`}
                      style={{
                        boxShadow: '0 4px 16px 0 rgba(0,0,0,0.08)',
                        background: style?.style === 'gradient'
                          ? '#1CA9E2'
                          : style?.iconBgColor && style?.iconBgColor.startsWith('bg-')
                            ? undefined
                            : style?.iconBgColor,
                      }}
                    >
                      <svg width="69" height="43" viewBox="0 0 69 43" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clipPath="url(#clip0_58_44)">
                          <path d="M30.3817 8.86725V19.3755C30.3817 20.1315 30.3817 20.9169 30.3699 21.7315V21.796C30.3699 32.5093 29.0905 43 14.7575 43C13.6549 43 12.6349 42.9473 11.6739 42.8242C15.5475 40.3627 18.1064 36.102 18.1064 31.2551C18.1064 30.9035 18.0946 30.5519 18.0651 30.2061H8.9264C4.0033 30.2061 0.00586589 26.2325 0.00586589 21.3388V8.86725C-3.00352e-05 3.97356 3.99741 0 8.9205 0H21.4611C26.3842 0 30.3817 3.97356 30.3817 8.86725ZM60.0794 0H47.5388C42.6157 0 38.6183 3.97356 38.6183 8.86725V21.333C38.6183 26.2267 42.6157 30.2002 47.5388 30.2002H56.6775C56.7129 30.5519 56.7188 30.9035 56.7188 31.2493C56.7188 36.0961 54.1658 40.3568 50.2863 42.8183C51.2414 42.9414 52.2673 42.9941 53.3699 42.9941C67.7088 42.9941 68.9823 32.5035 68.9823 21.7901V21.7256C68.9941 20.9169 68.9941 20.1257 68.9941 19.3696V8.86725C68.9941 3.97356 64.9966 0 60.0735 0H60.0794Z"
                            fill={style?.style === 'gradient' ? '#162343' : style?.iconBgColor === 'bg-[#9ee75d]' ? '#2B510A' : '#66CAFF'} />
                        </g>
                        <defs>
                          <clipPath id="clip0_58_44">
                            <rect width="69" height="43" fill="white"/>
                          </clipPath>
                        </defs>
                      </svg>
                    </div>
                    {/* Inner wrapper for background and overflow-hidden */}
                    <div
                      className={`p-6 pt-20 rounded-[20px] overflow-hidden${style?.style !== 'gradient' ? ' ' + style?.bgColor : ''}`}
                      style={
                        style?.style === 'gradient'
                          ? { background: 'linear-gradient(to bottom, #66CAFF 0%, rgba(102,202,255,0) 100%)' }
                          : undefined
                      }
                    >
                      <div className="font-['Poppins',Helvetica] font-normal text-black text-base">
                        <p className="mb-4">{testimonial.content}</p>
                        <p className="font-light italic">{testimonial.author}</p>
                      </div>
                    </div>
                  </Card>
                </div>
              );
            })}
          </div>
        </div>

        {/* No right column, illustration is absolutely positioned */}
      </div>
    </section>
  );
};
